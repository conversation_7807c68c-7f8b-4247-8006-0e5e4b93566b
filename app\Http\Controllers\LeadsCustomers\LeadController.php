<?php

namespace App\Http\Controllers\LeadsCustomers;

use App\Models\Lead\LeadFor;
use App\Models\Lead\LeadSource;
use Exception;
use Inertia\Inertia;
use App\Models\Lead\Lead;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class LeadController extends Controller
{

    private $tableName;

    public function __construct()
    {
        $this->tableName = (new Lead())->getTable();
    }


    /**
     * Display a listing of the resource.
     */

    public function index()
    {
        return Inertia::render('LeadsCustomers/Index');
        // if (!request()->user()->can($this->tableName . ".view")) {
        //     abort(403, "unauthorized");
        // }
        // try {
        //     // logic to fetch lead data and permissions and send to view
        //     $totalCount = $_GET['perPage'] ?? 10;
        //     $column = $_GET['column'] ?? 'id';
        //     $sortBy = $_GET['sort'] ?? 'desc';
        //     $leads = Lead::orderBy($column, $sortBy)->accessibleByUser("createdBy_id")->paginate($totalCount)->withQueryString();
        //     $data['leads'] = $leads;
        //     $data['getData'] = $_GET;
        //     $data["can_add"] = request()->user()->can($this->tableName . ".add");
        //     $data["can_edit"] = request()->user()->can($this->tableName . ".edit");
        //     $data["can_delete"] = request()->user()->can($this->tableName . ".delete");

        //     dd($data);
        //     return Inertia::render('LeadsCustomers/Index', ['collection' => $data]);
        // } catch (Exception $e) {
        //     return (request()->header('Accept') == 'application/json') ?
        //         response()->json(['message' => errorMessage($e->getCode()), 'status' => false])
        //         : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        // }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // if (!request()->user()->can($this->tableName . '.add')) {
        //     abort(403, "unauthorized");
        // }
        //
        $request->validate([
            "name" => "required",
            "mobile" => "required_without:email|min:10|max:15",
            "email" => "required_without:mobile|email",
            "pinCode" => "min:6|max:6"
        ], [
            "name.required" => "Name is required",
            "mobile.required_without" => "Mobile number is required when email is not provided",
            "email.required_without" => "Email is required when mobile is not provided",
            "email.email" => "Please enter a valid email address",
            "pinCode.min" => "Pin Code must be 6 digits",
            "pinCode.max" => "Pin Code must be 6 digits"
        ], [
            "name" => "Name",
            "mobile" => "Mobile",
            "email" => "Email",
            "pinCode" => "Pin Code"
        ]);

        dd($request->all());

        try {
            $lead = new Lead();
            $lead->name = $request->name;
            $lead->mobile = $request->mobile;
            $lead->email = $request->email;
            $lead->company = $request->company;
            $lead->city = $request->city;
            $lead->state = $request->state;
            $lead->country = $request->country;
            $lead->pinCode = $request->pinCode;
            $lead->lead_source_id = $request->leadSource;
            $lead->lead_for_id = $request->leadFor;
            $lead->assignedTo_id = $request->assignedToId;
            $lead->createdBy_id = Auth::id();
            $lead->save();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'Lead Created', 'status' => true])
                : redirect()->back()->with(["message" => 'Lead Created', "type" => "success"]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.  
     */
    public function destroy(string $id)
    {
        //
    }


    // helper functions
    public function getLeadSource(Request $request)
    {
        try {
            $leadSource = cache()->remember('lead_sources', 300, function () {
                return LeadSource::select(['id', 'name'])->get();
            });

            if ($leadSource->isEmpty())
                return response()->json(['message' => 'No lead sources found', 'status' => false]);


            return response()->json(['data' => $leadSource, 'status' => true]);
        } catch (Exception $e) {

            return request()->expectsJson()
                ? response()->json([
                    'message' => $e->getMessage(),
                    'status' => false
                ], 500)
                : redirect()->back()->with([
                    'message' => $e->getMessage(),
                    'type' => 'error'
                ]);
        }
    }
    public function getLeadFor(Request $request)
    {
        try {
            $leadFor = cache()->remember('lead_sources', 300, function () {
                return LeadFor::select(['id', 'name'])->get();
            });

            if ($leadFor->isEmpty())
                return response()->json(['message' => 'No lead sources found', 'status' => false]);

            return response()->json(['data' => $leadFor, 'status' => true]);
        } catch (Exception $e) {

            return request()->expectsJson()
                ? response()->json([
                    'message' => $e->getMessage(),
                    'status' => false
                ], 500)
                : redirect()->back()->with([
                    'message' => $e->getMessage(),
                    'type' => 'error'
                ]);
        }
    }
    public function getAgents(Request $request)
    {
        try {
            $agents = cache()->remember('agents', 300,  function () {
                return User::select(['id', DB::raw('username as name')])->get();
            });

            if ($agents->isEmpty())
                return response()->json(['message' => 'No agents found', 'status' => false]);

            return response()->json(['data' => $agents, 'status' => true]);
        } catch (Exception $e) {
            return request()->expectsJson()
                ? response()->json([
                    'message' => $e->getMessage(),
                    'status' => false
                ], 500)
                : redirect()->back()->with([
                    'message' => $e->getMessage(),
                    'type' => 'error'
                ]);
        }
    }
}
