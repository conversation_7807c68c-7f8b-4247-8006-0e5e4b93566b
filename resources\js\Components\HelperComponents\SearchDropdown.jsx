// global dropdown component with search feature 
import React, { useState, useEffect } from 'react';

const SearchDropdown = ({
    data,
    showImage = false,
    showRadio = false,
    singleRow = true,
    multiple = false,
    onChange,
    initialSelected = []
}) => {
    const [search, setSearch] = useState('');
    const [selectedItems, setSelectedItems] = useState(initialSelected);

    console.log("selected Item: ", selectedItems);
    

    useEffect(() => {
        // Update selected items when initialSelected changes
        setSelectedItems(initialSelected);
    }, [initialSelected]);

    const filtered = data?.filter(item =>
        item?.name?.toLowerCase().includes(search.toLowerCase())
    ) || [];

    const toggleSelect = (id, name) => {
        if (multiple) {
            setSelectedItems((prev) => {
                const exists = prev.find(i => i.id === id);
                const updated = exists 
                    ? prev.filter(i => i.id !== id) 
                    : [...prev, { id: id, name: name }];
                onChange?.(updated);
                return updated;
            });
        } else {
            const newSelection = { id: id, name: name };
            setSelectedItems([newSelection]);
            onChange?.([newSelection]);
        }
    };

    return (
        <div className="border rounded-md w-full max-w-md bg-white shadow-sm">
            <input
                type="text"
                placeholder="🔍 Search..."
                className="w-full p-2 border-gray-300 outline-none focus:ring-2 focus:ring-blue-500"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
            />

            <div className={`max-h-60 overflow-y-auto p-2 grid gap-2 ${
                singleRow ? 'grid-cols-1' : 'grid-cols-2 md:grid-cols-3'
            }`}>
                {filtered.map((item) => {
                    const isSelected = selectedItems.some(selected => selected.id === item.id);
                    return (
                        <label
                            key={item.id}
                            className={`flex items-center gap-2 cursor-pointer rounded p-2 hover:bg-gray-100 ${
                                isSelected ? 'bg-blue-50' : ''
                            }`}
                        >
                            {showRadio && (
                                <input
                                    type={multiple ? 'checkbox' : 'radio'}
                                    checked={isSelected}
                                    onChange={() => toggleSelect(item.id, item.name)}
                                    name="dropdown-selection"
                                />
                            )}
                            {showImage && item.image && (
                                <img
                                    src={item.image}
                                    alt={item.name}
                                    className="w-6 h-6 rounded-full object-cover"
                                />
                            )}
                            <span>{item.name}</span>
                        </label>
                    );
                })}
                {filtered.length === 0 && (
                    <div className="text-center text-gray-500 py-2">
                        No results found
                    </div>
                )}
            </div>
        </div>
    );
};

export default SearchDropdown;
