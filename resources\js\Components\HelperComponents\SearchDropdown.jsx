// global dropdown component with search feature
import React, { useState, useEffect, useMemo, useCallback } from 'react';

const SearchDropdown = React.memo(({
    data,
    showImage = false,
    showRadio = false,
    singleRow = true,
    multiple = false,
    onChange,
    initialSelected = [],
    onClose
}) => {
    const [search, setSearch] = useState('');
    const [selectedItems, setSelectedItems] = useState(initialSelected);

    useEffect(() => {
        // Update selected items when initialSelected changes
        setSelectedItems(initialSelected);
    }, [initialSelected]);

    // Memoize filtered data to prevent unnecessary recalculations
    const filtered = useMemo(() => {
        return data?.filter(item =>
            item?.name?.toLowerCase().includes(search.toLowerCase())
        ) || [];
    }, [data, search]);

    // Memoize toggle function to prevent recreation on every render
    const toggleSelect = useCallback((id, name) => {
        if (multiple) {
            setSelectedItems((prev) => {
                const exists = prev.find(i => i.id === id);
                const updated = exists
                    ? prev.filter(i => i.id !== id)
                    : [...prev, { id: id, name: name }];
                onChange?.(updated);
                return updated;
            });
        } else {
            const newSelection = { id: id, name: name };
            setSelectedItems([newSelection]);
            onChange?.(newSelection); // Pass single item for non-multiple mode
            onClose?.(); // Close dropdown after selection in single mode
        }
    }, [multiple, onChange, onClose]);

    // Memoize search input change handler
    const handleSearchChange = useCallback((e) => {
        setSearch(e.target.value);
    }, []);

    return (
        <div className="border rounded-md w-full max-w-md bg-white shadow-sm">
            <input
                type="text"
                placeholder="🔍 Search..."
                className="w-full p-2 border-gray-300 outline-none focus:ring-2 focus:ring-blue-500"
                value={search}
                onChange={handleSearchChange}
            />

            <div className={`max-h-60 overflow-y-auto p-2 grid gap-2 ${
                singleRow ? 'grid-cols-1' : 'grid-cols-2 md:grid-cols-3'
            }`}>
                {filtered.map((item) => {
                    const isSelected = selectedItems.some(selected => selected.id === item.id);
                    return (
                        <label
                            key={item.id}
                            className={`flex items-center gap-2 cursor-pointer rounded p-2 hover:bg-gray-100 ${
                                isSelected ? 'bg-blue-50' : ''
                            }`}
                        >
                            {showRadio && (
                                <input
                                    type={multiple ? 'checkbox' : 'radio'}
                                    checked={isSelected}
                                    onChange={() => toggleSelect(item.id, item.name)}
                                    name="dropdown-selection"
                                />
                            )}
                            {showImage && item.image && (
                                <img
                                    src={item.image}
                                    alt={item.name}
                                    className="w-6 h-6 rounded-full object-cover"
                                />
                            )}
                            <span>{item.name}</span>
                        </label>
                    );
                })}
                {filtered.length === 0 && (
                    <div className="text-center text-gray-500 py-2">
                        No results found
                    </div>
                )}
            </div>
        </div>
    );
});

export default SearchDropdown;
